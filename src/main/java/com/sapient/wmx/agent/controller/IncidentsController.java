package com.sapient.wmx.agent.controller;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class IncidentsController {

    private final ChatClient chatClient;

    public IncidentsController(ChatClient.Builder chatClient) {
        this.chatClient = chatClient.build();
    }

    public ChatResponse getIncidents(String query) {
        return chatClient.prompt()
                .user("Tell me interesting thing about Java")
                .call()
                .chatResponse();
    }
}
